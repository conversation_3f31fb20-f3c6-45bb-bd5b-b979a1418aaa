import 'package:flutter/material.dart';

/// Màu sắc cho ứng dụng với Glassmorphism theme
class AppColors {
  // === TEXT COLORS ===
  static const Color textPrimary = Colors.white;
  static const Color textSecondary = Color(0xFFE0E0E0);
  static const Color textSubtle = Color(0xFFB0B0B0);

  // === CARD COLORS ===
  static const Color cardPinkStart = Color(0xFFFF9EC7);
  static const Color cardPinkEnd = Color(0xFFFF6B9D);
  static const Color cardBlueStart = Color(0xFF9EC7FF);
  static const Color cardBlueEnd = Color(0xFF6B9DFF);
  static const Color cardPurpleStart = Color(0xFFB19CD9);
  static const Color cardPurpleEnd = Color(0xFF9C7FFF);

  // === BACKGROUND COLORS ===
  static const Color bgPurpleTop = Color(0xFF7758CE);
  static const Color bgBlueMiddle1 = Color(0xFF172679);
  static const Color bgBlueMiddle2 = Color(0xFF080E46);
  static const Color bgDarkBottom = Color(0xFF00000B);

  // === SURFACE COLORS ===
  static final Color surfaceOverlay = Colors.white.withValues(alpha: 0.1);
  static final Color surfaceOverlayDark = Colors.white.withValues(alpha: 0.05);

  // === SYSTEM COLORS ===
  static const Color primaryColor = Color(0xFFFF9EC7);
  static const Color secondaryColor = Color(0xFF9C7FFF);
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);
  static const Color accentColor = Color(0xFFE91E63);

  // === MATERIAL COLORS ===
  static const Color materialBrick = Color(0xFFF44336);
  static const Color materialSand = Color(0xFFFF9800);
  static const Color materialCement = Color(0xFF9E9E9E);
  static const Color materialSteel = Color(0xFF2196F3);
  static const Color materialStone = Color(0xFF795548);
  static const Color materialTile = Color(0xFFFF5722);
  static const Color materialGypsum = Color(0xFFFFFFFF);
  static const Color materialPaint = Color(0xFF9C27B0);
  static const Color materialDoor = Color(0xFF00BCD4);
  static const Color materialLabor = Color(0xFFE91E63);

  // === HELPER METHODS ===

  /// Lấy màu text primary dựa trên context
  static Color getTextPrimary(BuildContext context) {
    return textPrimary;
  }

  /// Lấy màu text secondary dựa trên context
  static Color getTextSecondary(BuildContext context) {
    return textSecondary;
  }

  /// Lấy màu text subtle dựa trên context
  static Color getTextSubtle(BuildContext context) {
    return textSubtle;
  }

  /// Lấy màu overlay surface dựa trên context
  static Color getSurfaceOverlay(BuildContext context) {
    return surfaceOverlay;
  }

  /// Lấy màu cho vật liệu dựa trên tên
  static Color getMaterialColor(String materialName) {
    switch (materialName.toLowerCase()) {
      case 'gạch':
      case 'gạch xây':
      case 'brick':
        return materialBrick;
      case 'cát':
      case 'cát xây':
      case 'cát trát':
      case 'sand':
        return materialSand;
      case 'xi măng':
      case 'cement':
        return materialCement;
      case 'thép':
      case 'steel':
        return materialSteel;
      case 'đá':
      case 'stone':
        return materialStone;
      case 'ngói':
      case 'tile':
        return materialTile;
      case 'thạch cao':
      case 'gypsum':
        return materialGypsum;
      case 'sơn':
      case 'paint':
        return materialPaint;
      case 'cửa':
      case 'door':
        return materialDoor;
      case 'nhân công':
      case 'nhân công xây dựng':
      case 'nhân công điện nước':
      case 'labor':
        return materialLabor;
      default:
        return cardPurpleStart;
    }
  }
}
