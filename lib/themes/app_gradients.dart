import 'package:flutter/material.dart';
import 'glass_colors.dart';

/// Đ<PERSON>nh nghĩa tất cả gradient cho ứng dụng - nguồn duy nhất cho gradient
class AppGradients {
  // === MAIN GRADIENTS ===

  /// Card Gradient chính (Pink to Purple)
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      GlassColors.cardPinkStart, // #FF9EC7
      GlassColors.cardPurpleEnd, // #9C7FFF
    ],
  );

  /// Background Gradient chính (Purple to Dark)
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0xFF7758CE), // bgPurpleTop
      Color(0xFF172679), // bgBlueMiddle1
      Color(0xFF080E46), // bgBlueMiddle2
      Color(0xFF00000B), // bgDarkBottom
    ],
    stops: [0.0, 0.3, 0.65, 1.0],
  );

  // === BUTTON GRADIENTS ===

  /// Button gradient chính
  static const LinearGradient buttonGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [GlassColors.cardPinkStart, GlassColors.cardPurpleEnd],
  );

  /// Soft button gradient với RadialGradient
  static RadialGradient get softButtonGradient => RadialGradient(
    center: const Alignment(-0.5, -0.6),
    radius: 1.8,
    colors: [
      Colors.purple.shade200.withValues(alpha: 0.56), // 0.7 * 0.8
      Colors.pink.shade200.withValues(alpha: 0.4), // 0.5 * 0.8
      Colors.orange.shade200.withValues(alpha: 0.4), // 0.5 * 0.8
      Colors.amber.shade100.withValues(alpha: 0.48), // 0.6 * 0.8
    ],
    stops: const [0.1, 0.4, 0.7, 0.9],
  );

  // === CARD VARIATIONS ===

  /// Card gradient ngược
  static const LinearGradient cardGradientReverse = LinearGradient(
    begin: Alignment.bottomRight,
    end: Alignment.topLeft,
    colors: [GlassColors.cardPinkStart, GlassColors.cardPurpleEnd],
  );

  /// Card gradient ngang
  static const LinearGradient cardGradientHorizontal = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [GlassColors.cardPinkStart, GlassColors.cardPurpleEnd],
  );

  /// Card gradient dọc
  static const LinearGradient cardGradientVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [GlassColors.cardPinkStart, GlassColors.cardPurpleEnd],
  );

  // === OVERLAY GRADIENTS ===

  /// Overlay gradient mờ
  static LinearGradient get overlayGradient => LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.white.withValues(alpha: 0.1),
      Colors.white.withValues(alpha: 0.05),
    ],
  );

  /// Overlay gradient tối
  static LinearGradient get overlayGradientDark => LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.white.withValues(alpha: 0.05),
      Colors.white.withValues(alpha: 0.02),
    ],
  );

  // === SEMANTIC GRADIENTS ===

  /// Success gradient (xanh lá)
  static const LinearGradient successGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF66BB6A), // Light green
      Color(0xFF4CAF50), // Green
    ],
  );

  /// Error gradient (đỏ)
  static const LinearGradient errorGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFEF5350), // Light red
      Color(0xFFF44336), // Red
    ],
  );

  /// Warning gradient (cam)
  static const LinearGradient warningGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFFFB74D), // Light orange
      Color(0xFFFF9800), // Orange
    ],
  );

  /// Info gradient (xanh dương)
  static const LinearGradient infoGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF42A5F5), // Light blue
      Color(0xFF2196F3), // Blue
    ],
  );

  // === HELPER METHODS ===

  /// Lấy overlay gradient dựa trên theme hiện tại
  static LinearGradient getOverlayGradient(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark
        ? overlayGradientDark
        : overlayGradient;
  }

  /// Tạo gradient tùy chỉnh từ 2 màu
  static LinearGradient createCustomGradient({
    required Color startColor,
    required Color endColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
    );
  }

  /// Tạo radial gradient tùy chỉnh
  static RadialGradient createCustomRadialGradient({
    required List<Color> colors,
    AlignmentGeometry center = Alignment.center,
    double radius = 1.0,
    List<double>? stops,
  }) {
    return RadialGradient(
      center: center,
      radius: radius,
      colors: colors,
      stops: stops,
    );
  }
}
