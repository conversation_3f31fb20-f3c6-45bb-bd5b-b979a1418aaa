import 'package:flutter/material.dart';

/// <PERSON><PERSON>u sắc cho Glassmorphism theme
class GlassColors {
  // === TEXT COLORS ===
  static const Color textPrimary = Colors.white;
  static const Color textSecondary = Color(0xFFE0E0E0);
  static const Color textSubtle = Color(0xFFB0B0B0);
  
  // === GLASS COLORS ===
  static final Color glassBackground = Colors.white.withOpacity(0.1);
  static final Color glassBorder = Colors.white.withOpacity(0.5);
  static final Color glassHighlight = Colors.white.withOpacity(0.8);
  
  // === ACCENT COLORS ===
  static const Color cardPinkStart = Color(0xFFFF9EC7);
  static const Color cardPinkEnd = Color(0xFFFF6B9D);
  static const Color cardBlueStart = Color(0xFF9EC7FF);
  static const Color cardBlueEnd = Color(0xFF6B9DFF);
  static const Color cardPurpleStart = Color(0xFFB19CD9);
  static const Color cardPurpleEnd = Color(0xFF9C7FFF);
  
  // === MATERIAL COLORS ===
  static const Color materialBrick = Color(0xFFF44336);
  static const Color materialSand = Color(0xFFFF9800);
  static const Color materialCement = Color(0xFF9E9E9E);
  static const Color materialSteel = Color(0xFF2196F3);
  static const Color materialStone = Color(0xFF795548);
  static const Color materialTile = Color(0xFFFF5722);
  static const Color materialGypsum = Color(0xFFFFFFFF);
  static const Color materialPaint = Color(0xFF9C27B0);
  static const Color materialDoor = Color(0xFF00BCD4);
  static const Color materialLabor = Color(0xFFE91E63);
  
  // === HELPER METHODS ===
  
  /// Lấy màu overlay surface dựa trên context
  static Color getSurfaceOverlay(BuildContext context) {
    return Colors.white.withOpacity(0.1);
  }
  
  /// Lấy màu cho vật liệu dựa trên tên
  static Color getMaterialColor(String materialName) {
    switch (materialName.toLowerCase()) {
      case 'gạch':
      case 'brick':
        return materialBrick;
      case 'cát':
      case 'cát xây':
      case 'cát trát':
      case 'sand':
        return materialSand;
      case 'xi măng':
      case 'cement':
        return materialCement;
      case 'thép':
      case 'steel':
        return materialSteel;
      case 'đá':
      case 'stone':
        return materialStone;
      case 'ngói':
      case 'tile':
        return materialTile;
      case 'thạch cao':
      case 'gypsum':
        return materialGypsum;
      case 'sơn':
      case 'paint':
        return materialPaint;
      case 'cửa':
      case 'door':
        return materialDoor;
      case 'nhân công':
      case 'labor':
        return materialLabor;
      default:
        return cardPurpleStart;
    }
  }
  
  /// Gradient cho card
  static LinearGradient get cardGradient => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Colors.white.withOpacity(0.2),
      Colors.white.withOpacity(0.1),
    ],
  );
  
  /// Gradient cho border
  static LinearGradient get borderGradient => LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Colors.white.withOpacity(0.5),
      Colors.white.withOpacity(0.3),
    ],
  );
}
