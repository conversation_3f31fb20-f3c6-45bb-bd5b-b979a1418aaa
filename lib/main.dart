import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'models/material_provider.dart';
import 'models/project/project_provider.dart';
import 'screens/home_screen.dart';
import 'services/price_storage_service.dart';
import 'themes/glassmorphism_theme.dart';
import 'widgets/glass_background.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Khởi tạo material provider
  final materialProvider = MaterialProvider();

  // Tải giá đã lưu
  final priceStorageService = PriceStorageService();
  final savedPrices = await priceStorageService.loadAllPrices();

  // Cập nhật giá nếu có bất kỳ giá nào đã được lưu
  if (savedPrices.isNotEmpty) {
    materialProvider.updateAllPrices(savedPrices);
  }

  runApp(MyApp(materialProvider: materialProvider));
}

class MyApp extends StatelessWidget {
  final MaterialProvider materialProvider;

  const MyApp({super.key, required this.materialProvider});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: materialProvider),
        ChangeNotifierProvider(create: (_) => ProjectProvider()),
      ],
      child: MaterialApp(
        title: 'Dự toán vật tư xây dựng',
        theme: GlassmorphismTheme.buildGlassmorphismTheme(),
        home: GlassBackground(child: const HomeScreen()),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
