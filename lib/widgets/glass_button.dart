import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';
import 'package:google_fonts/google_fonts.dart';

/// Button với hiệu ứng Glassmorphism
class GlassButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final double? width;
  final double? height;
  final IconData? icon;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final bool isEnabled;
  
  const GlassButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.width,
    this.height,
    this.icon,
    this.textColor,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
    this.isEnabled = true,
  });
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isEnabled ? onPressed : null,
      child: GlassmorphicContainer(
        width: width ?? 200,
        height: height ?? 50,
        borderRadius: 25,
        blur: 15,
        alignment: Alignment.center,
        border: 2,
        linearGradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isEnabled ? [
            Color(0xFFffffff).withOpacity(0.2),
            Color(0xFFffffff).withOpacity(0.1),
          ] : [
            Color(0xFFffffff).withOpacity(0.05),
            Color(0xFFffffff).withOpacity(0.02),
          ],
        ),
        borderGradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isEnabled ? [
            Color(0xFFffffff).withOpacity(0.8),
            Color(0xFFffffff).withOpacity(0.8),
          ] : [
            Color(0xFFffffff).withOpacity(0.3),
            Color(0xFFffffff).withOpacity(0.3),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: textColor ?? (isEnabled ? Colors.white : Colors.white.withOpacity(0.5)),
                size: fontSize + 2,
              ),
              SizedBox(width: 8),
            ],
            Text(
              text,
              style: GoogleFonts.poppins(
                fontSize: fontSize,
                fontWeight: fontWeight,
                color: textColor ?? (isEnabled ? Colors.white : Colors.white.withOpacity(0.5)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
