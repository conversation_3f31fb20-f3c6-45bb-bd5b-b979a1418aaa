import 'package:flutter/material.dart';
import 'package:glassmorphism/glassmorphism.dart';

/// Card với hiệu ứng Glassmorphism
class GlassCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final double borderRadius;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;

  const GlassCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.margin,
    this.padding,
    this.borderRadius = 20,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.all(8),
      child: GestureDetector(
        onTap: onTap,
        onLongPress: onLongPress,
        child: GlassmorphicContainer(
          width: width ?? double.infinity,
          height: height ?? 200,
          borderRadius: borderRadius,
          blur: 20,
          alignment: Alignment.bottomCenter,
          border: 2,
          linearGradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFffffff).withValues(alpha: 0.1),
              Color(0xFFffffff).withValues(alpha: 0.05),
            ],
          ),
          borderGradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFffffff).withValues(alpha: 0.5),
              Color(0xFFffffff).withValues(alpha: 0.5),
            ],
          ),
          child: Padding(padding: padding ?? EdgeInsets.all(16), child: child),
        ),
      ),
    );
  }
}
