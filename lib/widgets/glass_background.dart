import 'package:flutter/material.dart';

/// Widget nền với hiệu ứng gradient cho Glassmorphism UI
class GlassBackground extends StatelessWidget {
  final Widget child;
  
  const GlassBackground({super.key, required this.child});
  
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1a1a2e), // Tím đậm
            Color(0xFF16213e), // Xanh tím
            Color(0xFF0f3460), // Xanh đậm
          ],
        ),
      ),
      child: child,
    );
  }
}
